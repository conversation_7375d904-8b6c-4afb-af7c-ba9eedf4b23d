import {ReactNode} from 'react';
import {cookies} from 'next/headers';
import {getAuthUser} from '@/app/services/auth.service';
import AdminLayoutWrapper from '@/components/layouts/AdminLayoutWrapper';

interface UserLayoutProps {
    children: ReactNode;
}

export default async function UserLayout({children}: UserLayoutProps) {
    const authUser = await getAuthUser();

    const cookieStore = await cookies();
    const sidebarCollapsed = cookieStore.get('sidebarCollapsed')?.value;
    const isCollapsed = sidebarCollapsed ? JSON.parse(sidebarCollapsed) : false;

    return (
        <AdminLayoutWrapper initialCollapsed={isCollapsed} authUser={authUser}>
            {children}
        </AdminLayoutWrapper>
    );
}
