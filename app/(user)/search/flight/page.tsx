'use client';

import { useState } from 'react';
import { Button, DatePicker, Form, Select, Tabs } from 'antd';
import dayjs from 'dayjs';
import {
  FaPlane,
  FaExchangeAlt,
  FaUser,
} from 'react-icons/fa';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';

const { Option } = Select;

const FlightSearchPage = () => {
  const [form] = Form.useForm();
  const [tripType, setTripType] = useState('roundtrip');

  const airports = [
    { code: 'IST', name: 'İstanbul-Sabiha Gökçen SAW' },
    { code: 'ESB', name: 'Ankara-Esenboğa ESB' },
    { code: 'ADB', name: 'İzmir-Adnan Menderes ADB' },
    { code: 'AYT', name: '<PERSON><PERSON>ya-<PERSON>talya AYT' },
    { code: 'DLM', name: 'Dalaman-Dalaman DLM' },
  ];

  const handleSearch = (values: any) => {
    console.log('<PERSON><PERSON> verileri:', values);
  };

  const tabItems = [
    {
      key: 'oneway',
      label: '<PERSON><PERSON>',
    },
    {
      key: 'roundtrip',
      label: 'Gidiş-dö<PERSON>ş',
    },
    {
      key: 'multi',
      label: 'Çoklu Uçuş',
    },
  ];

  return (
    <>
      <HeaderBreadcrumb content="Uçuş Arama" links={[{ title: 'Uçuş Arama' }]} />

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-orange-50">
        <div className="mx-auto max-w-7xl px-4 py-8">
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-8">
                <h2 className="text-2xl font-bold text-blue-600">Uçuş Ara</h2>
                <span className="text-gray-400 cursor-pointer hover:text-gray-600">Check-in</span>
                <span className="text-gray-400 cursor-pointer hover:text-gray-600">Rezervasyonlarım</span>
                <span className="text-gray-400 cursor-pointer hover:text-gray-600">Benefitshop</span>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-6">
              <Tabs
                activeKey={tripType}
                onChange={setTripType}
                items={tabItems}
                className="flight-tabs mb-6"
              />

              <Form form={form} onFinish={handleSearch} className="flight-search-form">
                <div className="flex items-end space-x-4">
                  <div className="flex-1">
                    <div className="text-xs text-gray-500 mb-1">Nereden</div>
                    <Form.Item name="from" className="mb-0">
                      <Select
                        placeholder="İstanbul-Sabiha Gökçen SAW"
                        size="large"
                        className="flight-select-from"
                        showSearch
                        suffixIcon={<FaPlane className="text-gray-400" />}
                      >
                        {airports.map((airport) => (
                          <Option key={airport.code} value={airport.code}>
                            {airport.name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  <Button
                    type="text"
                    icon={<FaExchangeAlt />}
                    className="mb-0 h-10 w-10 rounded-full border border-gray-200 hover:border-blue-400 hover:text-blue-500"
                  />

                  <div className="flex-1">
                    <div className="text-xs text-gray-500 mb-1">Nereye</div>
                    <Form.Item name="to" className="mb-0">
                      <Select
                        placeholder="Nereye"
                        size="large"
                        className="flight-select-to"
                        showSearch
                        suffixIcon={<FaPlane className="text-gray-400" />}
                      >
                        {airports.map((airport) => (
                          <Option key={airport.code} value={airport.code}>
                            {airport.name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  <div className="flex-1">
                    <div className="text-xs text-gray-500 mb-1">Uçuş tarihi</div>
                    <Form.Item name="departureDate" className="mb-0">
                      <DatePicker
                        placeholder="03/08/2025"
                        size="large"
                        className="w-full"
                        format="DD/MM/YYYY"
                        disabledDate={(current) => current && current < dayjs().startOf('day')}
                      />
                    </Form.Item>
                  </div>

                  {tripType === 'roundtrip' && (
                    <div className="flex-1">
                      <div className="text-xs text-gray-500 mb-1">Dönüş Tarihi</div>
                      <Form.Item name="returnDate" className="mb-0">
                        <DatePicker
                          placeholder="06/08/2025"
                          size="large"
                          className="w-full"
                          format="DD/MM/YYYY"
                          disabledDate={(current) => current && current < dayjs().startOf('day')}
                        />
                      </Form.Item>
                    </div>
                  )}

                  <div className="flex-1">
                    <div className="text-xs text-gray-500 mb-1">Yolcu</div>
                    <Form.Item name="passengers" initialValue={1} className="mb-0">
                      <Select
                        size="large"
                        suffixIcon={<FaUser className="text-gray-400" />}
                      >
                        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
                          <Option key={num} value={num}>
                            {num}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  <div>
                    <Button
                      type="primary"
                      htmlType="submit"
                      size="large"
                      className="h-12 px-8 bg-orange-500 hover:bg-orange-600 border-0 rounded-lg font-semibold"
                    >
                      Uçuş Ara
                    </Button>
                  </div>
                </div>
              </Form>
            </div>
          </div>
        </div>
      </div>

      <style jsx global>{`
        .flight-tabs .ant-tabs-tab {
          border: none !important;
          background: transparent !important;
          color: #6b7280 !important;
          font-weight: 500 !important;
          padding: 8px 16px !important;
          margin-right: 24px !important;
        }

        .flight-tabs .ant-tabs-tab-active {
          color: #1f2937 !important;
          font-weight: 600 !important;
        }

        .flight-tabs .ant-tabs-ink-bar {
          background: #3b82f6 !important;
          height: 3px !important;
        }

        .flight-tabs .ant-tabs-content-holder {
          display: none !important;
        }

        .flight-select-from .ant-select-selector,
        .flight-select-to .ant-select-selector {
          border: 2px solid #e5e7eb !important;
          border-radius: 8px !important;
          height: 48px !important;
          padding: 8px 12px !important;
          transition: all 0.3s ease !important;
        }

        .flight-select-from:hover .ant-select-selector,
        .flight-select-to:hover .ant-select-selector {
          border-color: #3b82f6 !important;
        }

        .flight-select-from.ant-select-focused .ant-select-selector,
        .flight-select-to.ant-select-focused .ant-select-selector {
          border-color: #3b82f6 !important;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        .ant-picker {
          border: 2px solid #e5e7eb !important;
          border-radius: 8px !important;
          height: 48px !important;
          transition: all 0.3s ease !important;
        }

        .ant-picker:hover {
          border-color: #3b82f6 !important;
        }

        .ant-picker-focused {
          border-color: #3b82f6 !important;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }
      `}</style>
    </>
  );
};

export default FlightSearchPage;
