'use client';

import { useState } from 'react';
import { Button, DatePicker, Form, Select, Tabs } from 'antd';
import dayjs from 'dayjs';
import { FaExchangeAlt, FaPlane, FaUser } from 'react-icons/fa';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';

const { Option } = Select;

const FlightSearchPage = () => {
  const [form] = Form.useForm();
  const [tripType, setTripType] = useState('roundtrip');

  const airports = [
    { code: 'IST', name: 'İstanbul-Sabiha Gökçen SAW' },
    { code: 'ESB', name: 'Ankara-Esenboğa ESB' },
    { code: 'ADB', name: 'İzmir-Adnan Menderes ADB' },
    { code: 'AYT', name: '<PERSON>talya-Antalya AYT' },
    { code: 'DLM', name: 'Dalaman-Dalaman DLM' },
  ];

  const handleSearch = (values: any) => {
    console.log('Arama verileri:', values);
  };

  const tabItems = [
    {
      key: 'oneway',
      label: '<PERSON><PERSON>',
    },
    {
      key: 'roundtrip',
      label: 'Gidiş-dö<PERSON>',
    },
  ];

  return (
    <>
      <HeaderBreadcrumb content="Uçuş Arama" links={[{ title: 'Uçuş Arama' }]} />

      <div className="rounded-md border border-gray-100 bg-white p-8 shadow-sm m-4">
        <Tabs
          activeKey={tripType}
          onChange={setTripType}
          items={tabItems}
          className="mb-8 [&_.ant-tabs-content-holder]:hidden [&_.ant-tabs-ink-bar]:h-0.5 [&_.ant-tabs-ink-bar]:bg-blue-500 [&_.ant-tabs-tab]:mr-6 [&_.ant-tabs-tab]:border-none [&_.ant-tabs-tab]:bg-transparent [&_.ant-tabs-tab]:px-4 [&_.ant-tabs-tab]:py-2 [&_.ant-tabs-tab]:font-medium [&_.ant-tabs-tab]:text-gray-500 [&_.ant-tabs-tab-active]:font-semibold [&_.ant-tabs-tab-active]:text-gray-900"
        />

        <Form form={form} onFinish={handleSearch}>
          <div className="grid grid-cols-12 gap-3 items-end">
            <div className="col-span-12 md:col-span-3">
              <label className="mb-2 block text-xs font-medium text-gray-500">Nereden</label>
              <Form.Item name="from" className="mb-0">
                <Select
                  placeholder="İstanbul-Sabiha Gökçen SAW"
                  size="large"
                  className="w-full [&_.ant-select-selector]:h-12 [&_.ant-select-selector]:rounded-lg [&_.ant-select-selector]:border-2 [&_.ant-select-selector]:border-gray-200 [&_.ant-select-selector]:px-3 hover:[&_.ant-select-selector]:border-blue-400 [&.ant-select-focused_.ant-select-selector]:border-blue-500 [&.ant-select-focused_.ant-select-selector]:shadow-[0_0_0_3px_rgba(59,130,246,0.1)]"
                  showSearch
                  suffixIcon={<FaPlane className="text-gray-400" />}
                >
                  {airports.map((airport) => (
                    <Option key={airport.code} value={airport.code}>
                      {airport.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            <div className="col-span-12 md:col-span-1 flex justify-center">
              <Button
                type="text"
                icon={<FaExchangeAlt />}
                className="h-10 w-10 rounded-full border border-gray-200 hover:border-blue-400 hover:text-blue-500 flex items-center justify-center"
              />
            </div>

            <div className="col-span-12 md:col-span-3">
              <label className="mb-2 block text-xs font-medium text-gray-500">Nereye</label>
              <Form.Item name="to" className="mb-0">
                <Select
                  placeholder="Nereye"
                  size="large"
                  className="w-full [&_.ant-select-selector]:h-12 [&_.ant-select-selector]:rounded-lg [&_.ant-select-selector]:border-2 [&_.ant-select-selector]:border-gray-200 [&_.ant-select-selector]:px-3 hover:[&_.ant-select-selector]:border-blue-400 [&.ant-select-focused_.ant-select-selector]:border-blue-500 [&.ant-select-focused_.ant-select-selector]:shadow-[0_0_0_3px_rgba(59,130,246,0.1)]"
                  showSearch
                  suffixIcon={<FaPlane className="text-gray-400" />}
                >
                  {airports.map((airport) => (
                    <Option key={airport.code} value={airport.code}>
                      {airport.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            <div className="col-span-12 md:col-span-2">
              <label className="mb-2 block text-xs font-medium text-gray-500">Uçuş tarihi</label>
              <Form.Item name="departureDate" className="mb-0">
                <DatePicker
                  placeholder="03/08/2025"
                  size="large"
                  className="w-full [&_.ant-picker]:h-12 [&_.ant-picker]:rounded-lg [&_.ant-picker]:border-2 [&_.ant-picker]:border-gray-200 hover:[&_.ant-picker]:border-blue-400 [&.ant-picker-focused_.ant-picker]:border-blue-500 [&.ant-picker-focused_.ant-picker]:shadow-[0_0_0_3px_rgba(59,130,246,0.1)]"
                  format="DD/MM/YYYY"
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </div>

            {tripType === 'roundtrip' && (
              <div className="col-span-12 md:col-span-2">
                <label className="mb-2 block text-xs font-medium text-gray-500">Dönüş Tarihi</label>
                <Form.Item name="returnDate" className="mb-0">
                  <DatePicker
                    placeholder="06/08/2025"
                    size="large"
                    className="w-full [&_.ant-picker]:h-12 [&_.ant-picker]:rounded-lg [&_.ant-picker]:border-2 [&_.ant-picker]:border-gray-200 hover:[&_.ant-picker]:border-blue-400 [&.ant-picker-focused_.ant-picker]:border-blue-500 [&.ant-picker-focused_.ant-picker]:shadow-[0_0_0_3px_rgba(59,130,246,0.1)]"
                    format="DD/MM/YYYY"
                    disabledDate={(current) => current && current < dayjs().startOf('day')}
                  />
                </Form.Item>
              </div>
            )}

            <div className={`col-span-12 ${tripType === 'roundtrip' ? 'md:col-span-1' : 'md:col-span-3'}`}>
              <label className="mb-2 block text-xs font-medium text-gray-500">Yolcu</label>
              <Form.Item name="passengers" initialValue={1} className="mb-0">
                <Select
                  size="large"
                  className="w-full [&_.ant-select-selector]:h-12 [&_.ant-select-selector]:rounded-lg [&_.ant-select-selector]:border-2 [&_.ant-select-selector]:border-gray-200 [&_.ant-select-selector]:px-3 hover:[&_.ant-select-selector]:border-blue-400 [&.ant-select-focused_.ant-select-selector]:border-blue-500 [&.ant-select-focused_.ant-select-selector]:shadow-[0_0_0_3px_rgba(59,130,246,0.1)]"
                  suffixIcon={<FaUser className="text-gray-400" />}
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
                    <Option key={num} value={num}>
                      {num}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>

            <div className="col-span-12 md:col-span-2">
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                className="h-12 w-full rounded-lg border-0 bg-orange-500 font-semibold text-white hover:bg-orange-600"
              >
                Uçuş Ara
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
};

export default FlightSearchPage;
