'use client';

import { useState } from 'react';
import { <PERSON>ton, Card, DatePicker, Divider, Form, Radio, Select } from 'antd';
import dayjs from 'dayjs';
import {
  FaCalendarAlt,
  FaExchangeAlt,
  FaPlaneArrival,
  FaPlaneDeparture,
  FaSearch,
  FaStar,
  FaUser,
} from 'react-icons/fa';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';

const { RangePicker } = DatePicker;
const { Option } = Select;

const FlightSearchPage = () => {
  const [form] = Form.useForm();
  const [tripType, setTripType] = useState('roundtrip');

  const airports = [
    { code: 'IST', name: 'İstanbul Havalimanı', city: 'İstanbul', country: 'Türkiye' },
    { code: 'ESB', name: 'Esenboğa Havalimanı', city: 'Ankara', country: 'Türkiye' },
    { code: 'ADB', name: 'Adnan Menderes Havalimanı', city: 'İzmir', country: 'Türkiye' },
    { code: 'SAW', name: 'Sabiha Gökçen Havalimanı', city: 'İstanbul', country: 'Türkiye' },
    { code: 'AYT', name: 'Antalya Havalimanı', city: 'Antalya', country: 'Türkiye' },
    { code: 'DLM', name: 'Dalaman Havalimanı', city: 'Muğla', country: 'Türkiye' },
  ];

  const handleSearch = (values: any) => {
    console.log('Arama verileri:', {
      ...values,
      departureDate: values.dates[0],
      returnDate: tripType === 'roundtrip' ? values.dates[1] : null,
    });
  };

  const swapAirports = () => {
    const fromValue = form.getFieldValue('from');
    const toValue = form.getFieldValue('to');
    form.setFieldsValue({
      from: toValue,
      to: fromValue,
    });
  };

  return (
    <>
      <HeaderBreadcrumb content="Uçuş Arama" links={[{ title: 'Uçuş Arama' }]} />

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="mx-auto max-w-4xl px-4">
          <Card
            className="overflow-hidden rounded-2xl border-0 shadow-lg"
            styles={{ body: { padding: '32px' } }}
          >
            <Form form={form} layout="vertical" onFinish={handleSearch}>
              <div className="mb-8">
                <Radio.Group
                  value={tripType}
                  onChange={(e) => setTripType(e.target.value)}
                  className="w-full"
                  size="large"
                >
                  <div className="grid grid-cols-2 gap-4">
                    <Radio.Button
                      value="roundtrip"
                      className="flex h-12 items-center justify-center rounded-lg border-2 text-base font-medium"
                    >
                      <FaExchangeAlt className="mr-2" />
                      Gidiş-Dönüş
                    </Radio.Button>
                    <Radio.Button
                      value="oneway"
                      className="flex h-12 items-center justify-center rounded-lg border-2 text-base font-medium"
                    >
                      <FaPlaneDeparture className="mr-2" />
                      Tek Yön
                    </Radio.Button>
                  </div>
                </Radio.Group>
              </div>

              <div className="relative mb-8">
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                  <div className="relative">
                    <Form.Item
                      name="from"
                      label={
                        <span className="text-sm font-semibold tracking-wide text-gray-700 uppercase">
                          Nereden
                        </span>
                      }
                      rules={[{ required: true, message: 'Kalkış noktası seçin' }]}
                    >
                      <Select
                        placeholder="Kalkış havalimanı"
                        size="large"
                        className="flight-select"
                        showSearch
                        optionFilterProp="children"
                        suffixIcon={<FaPlaneDeparture className="text-blue-500" />}
                      >
                        {airports.map((airport) => (
                          <Option key={airport.code} value={airport.code}>
                            <div className="flex flex-col">
                              <span className="font-medium">{airport.name}</span>
                              <span className="text-xs text-gray-500">
                                {airport.city}, {airport.country} ({airport.code})
                              </span>
                            </div>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  <div className="relative">
                    <Form.Item
                      name="to"
                      label={
                        <span className="text-sm font-semibold tracking-wide text-gray-700 uppercase">
                          Nereye
                        </span>
                      }
                      rules={[{ required: true, message: 'Varış noktası seçin' }]}
                    >
                      <Select
                        placeholder="Varış havalimanı"
                        size="large"
                        className="flight-select"
                        showSearch
                        optionFilterProp="children"
                        suffixIcon={<FaPlaneArrival className="text-green-500" />}
                      >
                        {airports.map((airport) => (
                          <Option key={airport.code} value={airport.code}>
                            <div className="flex flex-col">
                              <span className="font-medium">{airport.name}</span>
                              <span className="text-xs text-gray-500">
                                {airport.city}, {airport.country} ({airport.code})
                              </span>
                            </div>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>
                </div>

                <Button
                  type="text"
                  icon={<FaExchangeAlt />}
                  onClick={swapAirports}
                  className="absolute top-8 left-1/2 z-10 h-10 w-10 -translate-x-1/2 transform rounded-full border-2 border-white bg-blue-500 text-white shadow-lg hover:bg-blue-600 lg:top-8"
                />
              </div>

              <div className="mb-8">
                <Form.Item
                  name="dates"
                  label={
                    <span className="text-sm font-semibold tracking-wide text-gray-700 uppercase">
                      Tarihler
                    </span>
                  }
                  rules={[{ required: true, message: 'Tarih seçin' }]}
                >
                  <RangePicker
                    size="large"
                    className="w-full"
                    disabledDate={(current) => current && current < dayjs().startOf('day')}
                    placeholder={[
                      'Gidiş Tarihi',
                      tripType === 'roundtrip' ? 'Dönüş Tarihi' : 'Tarih seçin',
                    ]}
                    format="DD MMMM YYYY"
                    suffixIcon={<FaCalendarAlt className="text-purple-500" />}
                  />
                </Form.Item>
              </div>

              <div className="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
                <Form.Item
                  name="passengers"
                  label={
                    <span className="text-sm font-semibold tracking-wide text-gray-700 uppercase">
                      Yolcu Sayısı
                    </span>
                  }
                  initialValue={1}
                >
                  <Select size="large" suffixIcon={<FaUser className="text-orange-500" />}>
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
                      <Option key={num} value={num}>
                        {num} {num === 1 ? 'Yolcu' : 'Yolcu'}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="class"
                  label={
                    <span className="text-sm font-semibold tracking-wide text-gray-700 uppercase">
                      Kabin Sınıfı
                    </span>
                  }
                  initialValue="economy"
                >
                  <Select size="large" suffixIcon={<FaStar className="text-yellow-500" />}>
                    <Option value="economy">
                      <div className="flex items-center">
                        <span>Ekonomi Sınıfı</span>
                      </div>
                    </Option>
                    <Option value="business">
                      <div className="flex items-center">
                        <span>Business Sınıfı</span>
                      </div>
                    </Option>
                    <Option value="first">
                      <div className="flex items-center">
                        <span>First Class</span>
                      </div>
                    </Option>
                  </Select>
                </Form.Item>
              </div>

              <Divider className="my-8" />

              <Form.Item className="mb-0">
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  icon={<FaSearch />}
                  className="h-14 w-full rounded-xl text-lg font-semibold"
                >
                  Uçuşları Ara
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </div>
      </div>
    </>
  );
};

export default FlightSearchPage;
