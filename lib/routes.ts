export const ROUTES = {
  // Ana sayfa
  HOME: '/',
  DASHBOARD: '/dashboard',
  AUTH: {
    LOGIN: '/login',
    LOGOUT: '/logout',
    REGISTER: '/register',
    AUTH_USER: '/auth-user',
  },

  PROFILE: {
    INDEX: '/profile/index',
    SHOW: (id?: number) => `/profile/index/${id}`,
    IDENTITY: {
      INDEX: '/profile/identity/index',
      CREATE: '/profile/identity/create',
      UPDATE: (id: number) => `/profile/identity/update/${id}`,
    },
    ADDRESS: {
      INDEX: '/profile/address/index',
      CREATE: '/profile/address/create',
      UPDATE: (id: number) => `/profile/address/update/${id}`,
    },
    PHONE: {
      INDEX: '/profile/phone/index',
      CREATE: '/profile/phone/create',
      UPDATE: (id: number) => `/profile/phone/update/${id}`,
    },
    CONTACT: '/profile/contact',
    PASSWORD: '/profile/password',
    DRIVER_LICENSE: '/profile/driver-license',
    PASSPORT: {
      INDEX: '/profile/passport/index',
      CREATE: '/profile/passport/create',
      UPDATE: (id: number) => `/profile/passport/update/${id}`,
    },
    VISA: {
      INDEX: '/profile/visa/index',
      CREATE: '/profile/visa/create',
      UPDATE: (id: number) => `/profile/visa/update/${id}`,
    },
    LOYALTY: {
      INDEX: '/profile/loyalty/index',
      CREATE: '/profile/loyalty/create',
      UPDATE: (id: number) => `/profile/loyalty/update/${id}`,
    },
  },

  // Kullanıcı Yönetimi
  USER: {
    INDEX: '/user/index',
    CREATE: '/user/create',
    UPDATE: (id: number) => `/user/update/${id}`,
  },
  USERGROUP: {
    INDEX: '/usergroup/index',
    CREATE: '/usergroup/create',
    UPDATE: (id: number) => `/usergroup/update/${id}`,
  },
  DEPARTMENT: {
    INDEX: '/department/index',
    CREATE: '/department/create',
    UPDATE: (id: number) => `/department/update/${id}`,
  },
  JOBTITLE: {
    INDEX: '/jobtitle/index',
    CREATE: '/jobtitle/create',
    UPDATE: (id: number) => `/jobtitle/update/${id}`,
  },

  // Firma ve Müşteri
  COMPANY: {
    INDEX: '/company/index',
    CREATE: '/company/create',
    EDIT: (id: number) => `/company/edit/${id}`,
    SHOW: (id: number) => `/company/show/${id}`,
  },
  COSTCENTER: {
    INDEX: '/costcenter/index',
    CREATE: '/costcenter/create',
    UPDATE: (id: number) => `/costcenter/update/${id}`,
  },
  BRANCH: {
    INDEX: '/branch/index',
    CREATE: '/branch/create',
    UPDATE: (id: number) => `/branch/update/${id}`,
  },
  GUEST: {
    INDEX: '/guest/index',
    CREATE: '/guest/create',
    UPDATE: (id: number) => `/guest/update/${id}`,
  },
  REASON: {
    INDEX: '/reason/index',
    CREATE: '/reason/create',
    UPDATE: (id: number) => `/reason/update/${id}`,
  },

  // Kısıtlama / İş Süreçleri
  POLICY: {
    INDEX: '/policy/index',
    CREATE: '/policy/create',
  },
  WORKFLOW: {
    INDEX: '/workflow/index',
    CREATE: '/workflow/create',
  },

  // Seyahat
  SEARCH: {
    FLIGHT: '/search/flight',
  },
};
