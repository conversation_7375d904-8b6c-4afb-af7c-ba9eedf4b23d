import { IconType } from 'react-icons';
import {
  <PERSON><PERSON><PERSON>ing,
  LuChartBarDecreasing,
  LuLayoutDashboard,
  LuUser,
  LuUsers,
} from 'react-icons/lu';
import { ROUTES } from '@/lib/routes';
import { BiCoinStack } from 'react-icons/bi';
import { AiOutlineUserSwitch } from 'react-icons/ai';
import { MdPolicy } from 'react-icons/md';
import { FaRegAddressBook, FaPlane } from 'react-icons/fa';
import { LiaBuildingSolid } from 'react-icons/lia';
import { enmUserOrganization } from '@/enums/UserOrganization';
import { PiFlowArrowFill } from 'react-icons/pi';
import { RiUserStarLine } from 'react-icons/ri';

export type MenuItem = {
  href: string;
  icon: IconType;
  label: string;
  isExpandable?: boolean;
  subItems?: {
    href: string;
    label: string;
  }[];
  organization?: enmUserOrganization[];
};

export type MenuGroup = {
  title: string;
  items: MenuItem[];
};

export const menuGroups: MenuGroup[] = [
  {
    title: 'NSM COMPANY',
    items: [
      {
        href: ROUTES.HOME,
        icon: LuLayoutDashboard,
        label: 'Anasayfa',
        isExpandable: false,
      },
    ],
  },
  {
    title: 'Seyahat',
    items: [
      {
        href: ROUTES.SEARCH.FLIGHT,
        icon: FaPlane,
        label: 'Uçak',
        isExpandable: false,
      },
    ],
  },
  {
    title: 'Kullanıcı Yönetimi',
    items: [
      {
        href: ROUTES.USER.INDEX,
        icon: LuUser,
        label: 'Personel Bilgileri',
        isExpandable: true,
        subItems: [
          { href: ROUTES.USER.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.USER.INDEX, label: 'Tüm Personeller' },
        ],
      },
      {
        href: ROUTES.USERGROUP.INDEX,
        icon: LuUsers,
        label: 'Personel Grupları',
        isExpandable: true,
        subItems: [
          { href: ROUTES.USERGROUP.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.USERGROUP.INDEX, label: 'Personel Grupları' },
        ],
      },
      {
        href: ROUTES.JOBTITLE.INDEX,
        icon: RiUserStarLine,
        label: 'Personel Ünvanları',
        isExpandable: true,
        subItems: [
          { href: ROUTES.JOBTITLE.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.JOBTITLE.INDEX, label: 'Tüm Ünvanlar' },
        ],
      },
      {
        href: ROUTES.GUEST.INDEX,
        icon: AiOutlineUserSwitch,
        label: 'Misafir İşlemleri',
        isExpandable: true,
        subItems: [
          { href: ROUTES.GUEST.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.GUEST.INDEX, label: 'Tüm Misafirler' },
        ],
      },
    ],
  },
  {
    title: 'Firma ve Müşteri',
    items: [
      {
        organization: [enmUserOrganization.AGENCY],
        href: ROUTES.COMPANY.INDEX,
        icon: LiaBuildingSolid,
        label: 'Firma Bilgileri',
        isExpandable: true,
        subItems: [
          { href: ROUTES.COMPANY.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.COMPANY.INDEX, label: 'Tüm Firmalar' },
        ],
      },
      {
        href: ROUTES.DEPARTMENT.INDEX,
        icon: LuBuilding,
        label: 'Departman İşlemleri',
        isExpandable: true,
        subItems: [
          { href: ROUTES.DEPARTMENT.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.DEPARTMENT.INDEX, label: 'Tüm Departmanlar' },
        ],
      },
      {
        href: ROUTES.COSTCENTER.INDEX,
        icon: BiCoinStack,
        label: 'Masraf Merkezleri',
        isExpandable: true,
        subItems: [
          { href: ROUTES.COSTCENTER.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.COSTCENTER.INDEX, label: 'Tüm Masraf Merkezleri' },
        ],
      },
      {
        href: ROUTES.BRANCH.INDEX,
        icon: FaRegAddressBook,
        label: 'Şube İşlemleri',
        isExpandable: true,
        subItems: [
          { href: ROUTES.BRANCH.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.BRANCH.INDEX, label: 'Tüm Şubeler' },
        ],
      },
    ],
  },
  {
    title: 'Kısıtlama / İş Süreci',
    items: [
      {
        href: ROUTES.POLICY.INDEX,
        icon: MdPolicy,
        label: 'Kısıtlamalar',
        isExpandable: true,
        subItems: [
          { href: ROUTES.POLICY.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.POLICY.INDEX, label: 'Tüm Kısıtlamalar' },
        ],
      },
      {
        href: ROUTES.WORKFLOW.INDEX,
        icon: PiFlowArrowFill,
        label: 'İş Süreci',
        isExpandable: true,
        subItems: [
          { href: ROUTES.WORKFLOW.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.WORKFLOW.INDEX, label: 'Tüm Süreçler' },
        ],
      },
      {
        href: ROUTES.REASON.INDEX,
        icon: LuChartBarDecreasing,
        label: 'Açıklamalar',
        isExpandable: true,
        subItems: [
          { href: ROUTES.REASON.CREATE, label: 'Yeni Ekle' },
          { href: ROUTES.REASON.INDEX, label: 'Tüm Açıklamalar' },
        ],
      },
    ],
  },
];
